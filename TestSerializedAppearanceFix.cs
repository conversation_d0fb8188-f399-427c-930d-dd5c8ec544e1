using System;
using System.Collections.Generic;

namespace TestFix
{
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("Testing SerializedAppearance Export/Import Fix");
            
            // Simulate the original test data
            string[] originalAppearance = new[] { "appearance1", "appearance2", "appearance3" };
            
            // Simulate the Export method behavior
            string exportedString = string.Join(";\n", originalAppearance).Replace("\"", "\'");
            Console.WriteLine($"Exported string: '{exportedString}'");
            Console.WriteLine($"Exported string length: {exportedString.Length}");
            
            // Show what the old Import method would produce
            string[] oldImportResult = exportedString.Split(';');
            Console.WriteLine("\nOld Import method result:");
            for (int i = 0; i < oldImportResult.Length; i++)
            {
                Console.WriteLine($"  [{i}]: '{oldImportResult[i]}' (length: {oldImportResult[i].Length})");
            }
            
            // Show what the new Import method produces
            string[] newImportResult = exportedString.Split(new[] { ";\n", ";" }, StringSplitOptions.None);
            Console.WriteLine("\nNew Import method result:");
            for (int i = 0; i < newImportResult.Length; i++)
            {
                Console.WriteLine($"  [{i}]: '{newImportResult[i]}' (length: {newImportResult[i].Length})");
            }
            
            // Test equality
            Console.WriteLine("\nEquality test:");
            Console.WriteLine($"Original length: {originalAppearance.Length}");
            Console.WriteLine($"Old import length: {oldImportResult.Length}");
            Console.WriteLine($"New import length: {newImportResult.Length}");
            
            bool oldMethodMatches = AreArraysEqual(originalAppearance, oldImportResult);
            bool newMethodMatches = AreArraysEqual(originalAppearance, newImportResult);
            
            Console.WriteLine($"Old method matches original: {oldMethodMatches}");
            Console.WriteLine($"New method matches original: {newMethodMatches}");
            
            if (newMethodMatches)
            {
                Console.WriteLine("\n✅ Fix is working correctly!");
            }
            else
            {
                Console.WriteLine("\n❌ Fix is not working correctly.");
            }
        }
        
        static bool AreArraysEqual(string[] array1, string[] array2)
        {
            if (array1.Length != array2.Length)
                return false;
                
            for (int i = 0; i < array1.Length; i++)
            {
                if (array1[i] != array2[i])
                {
                    Console.WriteLine($"  Mismatch at index {i}: '{array1[i]}' vs '{array2[i]}'");
                    return false;
                }
            }
            return true;
        }
    }
}
